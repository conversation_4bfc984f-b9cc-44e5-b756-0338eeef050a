import { FC, useState } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData, addNewElement } from "./mttoAccTemplateMockData";
import { TableData } from "../MetlifeComponents/Table/table-types";

type MttoAccTemplateProps = {};

const MttoAccTemplate: FC<MttoAccTemplateProps> = () => {
  const handleSave = (element: TableData) => {
    console.log("Guardando elemento:", element);
    // Aquí se implementaría la lógica para guardar el nuevo elemento
  };

  const handleEdit = (element: TableData) => {
    console.log("Editando elemento:", element);
    // Aquí se implementaría la lógica para editar el elemento
  };

  const handleDelete = (elementId: number) => {
    console.log("Eliminando elemento con ID:", elementId);
    // Aquí se implementaría la lógica para eliminar el elemento
  };

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE PLANTILLAS CONTABLES</h1>
      <Table
        id="accTemplates"
        data={mockData}
        columns={mockColumns}
        filters={[
          "company",
          "area",
          "description",
          "concept",
          "movementType",
          "module",
          "templateId",
        ]}
        onSave={handleSave}
        onEdit={handleEdit}
        onDelete={handleDelete}
        enableAddRow={true}
      />
    </div>
  );
};

export default MttoAccTemplate;
